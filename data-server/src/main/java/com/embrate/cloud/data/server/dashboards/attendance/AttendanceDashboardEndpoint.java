package com.embrate.cloud.data.server.dashboards.attendance;

import com.embrate.cloud.core.api.dashboards.attendance.StaffSummaryWithAttendanceOrgStats;
import com.embrate.cloud.core.lib.dashboards.attendance.AttendanceDashboardManager;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.UUID;

/**
 * <AUTHOR>
 **/
@Path("2.1/dashboards/attendance")
public class AttendanceDashboardEndpoint {

	private final AttendanceDashboardManager attendanceDashboardManager;

	public AttendanceDashboardEndpoint(AttendanceDashboardManager attendanceDashboardManager) {
		this.attendanceDashboardManager = attendanceDashboardManager;
	}

	@GET
	@Path("organization/{organization_id}/students")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentAttendanceOrganizationStats(@PathParam("organization_id") UUID organizationId,
										 @QueryParam("institute_ids") String selectedInstituteIds,
										 @QueryParam("user_id") UUID userId, @QueryParam("start_date") int startDate,
										 @QueryParam("end_date") int endDate) {
		final StaffSummaryWithAttendanceOrgStats attendanceOrgStats = attendanceDashboardManager.getStudentAttendanceOrgStats(organizationId, selectedInstituteIds, userId, startDate,
				endDate);
		return Response.status(Response.Status.OK.getStatusCode()).entity(attendanceOrgStats).build();
	}
}
