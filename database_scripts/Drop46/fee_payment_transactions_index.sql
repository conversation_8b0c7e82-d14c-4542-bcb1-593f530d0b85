ALTER TABLE fee_payment_transactions
ADD COLUMN t_paid_amount DOUBLE NOT NULL DEFAULT 0,
ADD COLUMN t_instant_discount_amount DOUBLE NOT NULL DEFAULT 0,
ADD COLUMN t_fine_amount DOUBLE NOT NULL DEFAULT 0;

--ALTER TABLE fee_payment_transactions CHANGE transaction_paid_amount t_paid_amount DOUBLE NOT NULL DEFAULT 0;
--ALTER TABLE fee_payment_transactions CHANGE transaction_instant_discount_amount t_instant_discount_amount DOUBLE NOT NULL DEFAULT 0;
--ALTER TABLE fee_payment_transactions CHANGE transaction_fine_amount t_fine_amount DOUBLE NOT NULL DEFAULT 0;


--Run this query so that your updated_at columns is not updated by default
ALTER TABLE fee_payment_transactions MODIFY updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

UPDATE fee_payment_transactions fpt
JOIN (
    SELECT
        transaction_id,
        SUM(paid_amount) AS t_paid_amount,
        SUM(instant_discount_amount) AS t_instant_discount_amount,
        SUM(fine_amount) AS t_fine_amount
    FROM fee_payment_transaction_amounts
    GROUP BY transaction_id
) AS tta ON fpt.transaction_id = tta.transaction_id
SET
    fpt.t_paid_amount = tta.t_paid_amount,
    fpt.t_instant_discount_amount = tta.t_instant_discount_amount,
    fpt.t_fine_amount = tta.t_fine_amount;


--Run this query to revert back
ALTER TABLE fee_payment_transactions MODIFY updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;


ALTER TABLE `fee_payment_transactions` DROP INDEX `composite_index_2`;
ALTER TABLE `fee_payment_transactions` ADD INDEX `composite_index_2` (`institute_id`,`academic_session_id`,`status`,`updated_at`);



--SELECT fee_payment_transactions.*, (fee_payment_transactions.t_paid_amount + fee_payment_transactions.t_fine_amount - fee_payment_transactions.debit_wallet_amount) AS net_paid_amount, student_academic_session_details.session_status FROM fee_payment_transactions
--JOIN students ON fee_payment_transactions.student_id = students.student_id
--JOIN student_academic_session_details ON student_academic_session_details.student_id = students.student_id AND student_academic_session_details.academic_session_id = 198
--WHERE fee_payment_transactions.institute_id = 10160 AND fee_payment_transactions.academic_session_id = 198 AND fee_payment_transactions.status = 'ACTIVE'
--ORDER BY fee_payment_transactions.updated_at DESC LIMIT 10 OFFSET 0;
--
--
--SELECT fee_payment_transactions.*, students.*, (fee_payment_transactions.t_paid_amount + fee_payment_transactions.t_fine_amount - fee_payment_transactions.debit_wallet_amount) AS net_paid_amount, student_academic_session_details.session_status FROM fee_payment_transactions
--JOIN students ON fee_payment_transactions.student_id = students.student_id
--JOIN student_academic_session_details ON student_academic_session_details.student_id = students.student_id AND student_academic_session_details.academic_session_id = 198
--WHERE fee_payment_transactions.institute_id = 10160 AND fee_payment_transactions.academic_session_id = 198 AND fee_payment_transactions.status = 'ACTIVE'
--ORDER BY fee_payment_transactions.updated_at DESC LIMIT 10 OFFSET 0;


--SELECT
--    students.student_id,
--    students.name,
--    students.admission_number,
--    students.father_name,
--    standards.standard_name,
--    standards.stream,
--    standard_section_mapping.section_name
--FROM students
--JOIN student_academic_session_details
--    ON student_academic_session_details.academic_session_id = 198
--    AND students.student_id = student_academic_session_details.student_id
--JOIN standards
--    ON student_academic_session_details.standard_id = standards.standard_id
--LEFT JOIN standard_section_mapping
--    ON standard_section_mapping.standard_id = standards.standard_id
--    AND standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id
--    AND student_academic_session_details.section_id = standard_section_mapping.section_id
--WHERE students.institute_id = 10160
--AND student_academic_session_details.session_status IN ('ENROLLED')
--AND CONCAT_WS('/', students.name, students.admission_number, students.registration_number, students.father_name, standards.standard_name) LIKE '%a%';
