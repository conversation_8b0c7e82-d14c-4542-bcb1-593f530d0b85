package com.embrate.cloud.core.api.dashboards.fees;

import com.embrate.cloud.core.api.dashboards.common.InstituteValue;

import java.util.List;

/**
 * <AUTHOR>
 */
public class StudentFeesOrgStats {

	private final List<PaymentModeAmount> paymentModeAmounts;

	private final int totalNewAdmissions;

	private final List<InstituteValue> tcIssued;

	private final int totalTCIssued;

	public StudentFeesOrgStats(List<InstituteValue> newAdmissions, int totalNewAdmissions, List<InstituteValue> tcIssued, int totalTCIssued) {
		this.newAdmissions = newAdmissions;
		this.totalNewAdmissions = totalNewAdmissions;
		this.tcIssued = tcIssued;
		this.totalTCIssued = totalTCIssued;
	}


	public List<InstituteValue> getNewAdmissions() {
		return newAdmissions;
	}

	public int getTotalNewAdmissions() {
		return totalNewAdmissions;
	}

	public List<InstituteValue> getTcIssued() {
		return tcIssued;
	}

	public int getTotalTCIssued() {
		return totalTCIssued;
	}

	@Override
	public String toString() {
		return "StudentAdmissionOrgStats{" +
				"newAdmissions=" + newAdmissions +
				", totalNewAdmissions=" + totalNewAdmissions +
				", tcIssued=" + tcIssued +
				", totalTCIssued=" + totalTCIssued +
				'}';
	}
}
