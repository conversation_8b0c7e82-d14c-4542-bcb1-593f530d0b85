package com.embrate.cloud.core.api.dashboards.fees;

import com.lernen.cloud.core.api.common.TransactionMode;

/**
 * <AUTHOR>
 */
public class PaymentModeAmount {

	private final TransactionMode mode;

	private final double value;

	public PaymentModeAmount(TransactionMode mode, double value) {
		this.mode = mode;
		this.value = value;
	}

	public TransactionMode getMode() {
		return mode;
	}

	public double getValue() {
		return value;
	}

	@Override
	public String toString() {
		return "PaymentModeAmount{" +
				"mode=" + mode +
				", value=" + value +
				'}';
	}
}
