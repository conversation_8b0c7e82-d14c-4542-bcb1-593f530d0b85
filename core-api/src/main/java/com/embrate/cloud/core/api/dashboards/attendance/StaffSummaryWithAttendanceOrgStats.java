package com.embrate.cloud.core.api.dashboards.attendance;

import com.embrate.cloud.core.api.dashboards.admission.StaffCountByGender;
import com.embrate.cloud.core.api.dashboards.common.InstituteValue;

import java.util.List;

/**
 * <AUTHOR>
 */
public class UserSummaryWithAttendanceOrgStats {

	private final List<InstituteValue> studentCount;
	private final List<InstituteValue> staffCount;
	private final List<StaffCountByGender> staffCountByGender;
	private final int totalStudent;
	private final int totalStaff;
	private final List<AttendanceTypeOrgCounts> studentAttendanceCounts;
	private final List<AttendanceTypeOrgCounts> staffAttendanceCounts;

	public UserSummaryWithAttendanceOrgStats(List<InstituteValue> studentCount, List<InstituteValue> staffCount, List<StaffCountByGender> staffCountByGender, int totalStudent, int totalStaff, List<AttendanceTypeOrgCounts> studentAttendanceCounts, List<AttendanceTypeOrgCounts> staffAttendanceCounts) {
		this.studentCount = studentCount;
		this.staffCount = staffCount;
		this.staffCountByGender = staffCountByGender;
		this.totalStudent = totalStudent;
		this.totalStaff = totalStaff;
		this.studentAttendanceCounts = studentAttendanceCounts;
		this.staffAttendanceCounts = staffAttendanceCounts;
	}

	public List<InstituteValue> getStudentCount() {
		return studentCount;
	}

	public List<InstituteValue> getStaffCount() {
		return staffCount;
	}

	public List<StaffCountByGender> getStaffCountByGender() {
		return staffCountByGender;
	}

	public int getTotalStudent() {
		return totalStudent;
	}

	public int getTotalStaff() {
		return totalStaff;
	}

	public List<AttendanceTypeOrgCounts> getStudentAttendanceCounts() {
		return studentAttendanceCounts;
	}

	public List<AttendanceTypeOrgCounts> getStaffAttendanceCounts() {
		return staffAttendanceCounts;
	}

	@Override
	public String toString() {
		return "UserSummaryWithAttendanceOrgStats{" +
				"studentCount=" + studentCount +
				", staffCount=" + staffCount +
				", staffCountByGender=" + staffCountByGender +
				", totalStudent=" + totalStudent +
				", totalStaff=" + totalStaff +
				", studentAttendanceCounts=" + studentAttendanceCounts +
				", staffAttendanceCounts=" + staffAttendanceCounts +
				'}';
	}
}
