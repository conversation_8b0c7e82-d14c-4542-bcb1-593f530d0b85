package com.embrate.cloud.core.api.dashboards.attendance;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AttendanceTypeOrgCounts {
	private final String attendanceType;

	private final List<InstituteAttendanceStats> instituteStats;

	private final List<AttendanceCount> attendanceCounts;


	public AttendanceTypeOrgCounts(String attendanceType, List<InstituteAttendanceStats> instituteStats, List<AttendanceCount> attendanceCounts) {
		this.attendanceType = attendanceType;
		this.instituteStats = instituteStats;
		this.attendanceCounts = attendanceCounts;
	}

	public String getAttendanceType() {
		return attendanceType;
	}

	public List<InstituteAttendanceStats> getInstituteStats() {
		return instituteStats;
	}

	public List<AttendanceCount> getAttendanceCounts() {
		return attendanceCounts;
	}

	@Override
	public String toString() {
		return "AttendanceTypeOrgCounts{" +
				"attendanceType='" + attendanceType + '\'' +
				", instituteStats=" + instituteStats +
				", attendanceCounts=" + attendanceCounts +
				'}';
	}
}