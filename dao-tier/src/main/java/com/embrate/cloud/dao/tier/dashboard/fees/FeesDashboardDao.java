package com.embrate.cloud.dao.tier.dashboard.fees;

import com.embrate.cloud.core.api.dashboards.admission.StaffCountByGender;
import com.embrate.cloud.core.api.dashboards.admission.StudentAdmTCCount;
import com.embrate.cloud.core.api.dashboards.admission.StudentCountSummary;
import com.embrate.cloud.core.api.dashboards.fees.FeeCollectionByPaymentMode;
import com.embrate.cloud.dao.tier.dashboard.admission.mappers.StaffCountByGenderRowMapper;
import com.embrate.cloud.dao.tier.dashboard.fees.mapper.FeeCollectionRowMapper;
import com.lernen.cloud.core.api.student.StudentSessionSummary;
import com.lernen.cloud.dao.tier.student.mappers.StudentAdmTCCountRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentCountSummaryRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentSessionSummaryRowMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.CollectionUtils;

import java.sql.Date;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * DAO class for student session summary queries
 *
 * <AUTHOR>
 */
public class FeesDashboardDao {

	private static final Logger logger = LogManager.getLogger(FeesDashboardDao.class);

	private static final FeeCollectionRowMapper FEE_COLLECTION_ROW_MAPPER = new FeeCollectionRowMapper();


	private static final String GET_FEES_COLLECTED_BY_TRANSACTION_MODE = "select institute_id, transaction_mode, sum(t_paid_amount) from fee_payment_transactions where " +
			"institute_id in %s and transaction_date between ? and ? group by 1, 2";

	private static final String GET_FEES_COLLECTED_BY_TRANSACTION_MODE_FEE_HEAD = "select fee_payment_transactions.institute_id, transaction_mode, fee_head, " +
			" sum(paid_amount) t_amount from fee_payment_transactions join " +
			" fee_payment_transaction_amounts on fee_payment_transactions.transaction_id = fee_payment_transaction_amounts.transaction_id " +
			" join fee_head_configuration on fee_payment_transaction_amounts.fee_head_id = fee_head_configuration.fee_head_id " +
			" where fee_payment_transactions.institute_id in %s and transaction_date between ? and ? group by 1, 2, 3";

	private final JdbcTemplate jdbcTemplate;

	public FeesDashboardDao(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}

	/**
	 * Helper method to create parameterized IN clause placeholders
	 *
	 * @param size Number of parameters
	 * @return String with comma-separated question marks (e.g., "?,?,?")
	 */
	private String createInClausePlaceholders(int size) {
		return String.join(",", Collections.nCopies(size, "?"));
	}

	private Date convertToSqlDate(int unixTimestamp) {
		return new Date(unixTimestamp * 1000L);
	}

	private Object[] createParameterArray(List<Integer> instituteIds, Date startDate, Date endDate) {
		List<Object> params = new ArrayList<>();
		params.addAll(instituteIds);
		if (startDate != null) {
			params.add(startDate);
		}
		if (endDate != null) {
			params.add(endDate);
		}
		return params.toArray();
	}

	//GET_FEES_COLLECTED_BY_TRANSACTION_MODE
	public List<FeeCollectionByPaymentMode> getFeesCollectedByTransactionMode(List<Integer> instituteIds,
																			  int startDate,
																			  int endDate) {
		if (CollectionUtils.isEmpty(instituteIds)) {
			logger.warn("Institute IDs are empty");
			return null;
		}

		if (startDate <= 0 || endDate <= 0 || startDate > endDate) {
			logger.warn("Start date {} or end date {} is invalid, instituteIds {}", startDate, endDate, instituteIds);
			return null;
		}

		try {
			// Create parameterized query with placeholders
			String institutePlaceholders = createInClausePlaceholders(instituteIds.size());
			String query = String.format(GET_FEES_COLLECTED_BY_TRANSACTION_MODE, institutePlaceholders);

			// Convert dates and create parameter array
			Date sqlStartDate = convertToSqlDate(startDate);
			Date sqlEndDate = convertToSqlDate(endDate);
			Object[] params = createParameterArray(instituteIds, sqlStartDate, sqlEndDate);

			logger.debug("Executing parameterized query: {} with {} parameters", query, params.length);
			return jdbcTemplate.query(query, params, FEE_COLLECTION_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting fees collected by transaction mode for institutes {} between {} and {}",
					instituteIds, startDate, endDate, e);
		}
		return null;
	}

}
