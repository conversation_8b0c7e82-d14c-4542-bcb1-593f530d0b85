package com.embrate.cloud.dao.tier.dashboard.fees.mapper;

import com.embrate.cloud.core.api.dashboards.attendance.StaffAttendanceCount;
import com.embrate.cloud.core.api.dashboards.fees.FeeCollectionByPaymentMode;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.common.TransactionMode;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for StudentAttendanceCount
 * Maps database results from attendance count queries to StudentAttendanceCount POJO
 *
 * <AUTHOR>
 */
public class FeeCollectionRowMapper implements RowMapper<FeeCollectionByPaymentMode> {

	protected static final String INSTITUTE_ID = "institute_id";
	protected static final String TRANSACTION_MODE = "transaction_mode";
	protected static final String T_AMOUNT = "t_amount";

	@Override
	public FeeCollectionByPaymentMode mapRow(ResultSet rs, int rowNum) throws SQLException {
		int instituteId = rs.getInt(INSTITUTE_ID);
		TransactionMode transactionMode = TransactionMode.getTransactionMode(rs.getString(TRANSACTION_MODE));
		double amount = rs.getDouble(T_AMOUNT);

		return new FeeCollectionByPaymentMode(instituteId, transactionMode, amount);
	}
}
